# ColorSpace Multispectral XM - 开发指南

## 开发环境设置

### 系统要求
- **操作系统**: Windows 10/11 (推荐)
- **Python版本**: Python 3.8+ 
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 至少5GB可用空间

### 依赖安装

#### 核心依赖
```bash
pip install PyQt5
pip install numpy
pip install scipy
pip install matplotlib
pip install pyqtgraph
pip install pyserial
pip install openpyxl
```

#### 开发工具
```bash
pip install pyinstaller  # 用于打包
pip install pytest       # 用于测试
pip install black        # 代码格式化
pip install flake8       # 代码检查
```

### 项目设置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd ColorSpace_Multispectral_XM_cheng_2
```

#### 2. 创建虚拟环境
```bash
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac
```

#### 3. 安装依赖
```bash
pip install -r requirements.txt  # 如果有requirements.txt
```

## 项目结构理解

### 核心模块关系
```
main.py (主程序)
├── UI层
│   ├── mainwindow.ui (界面设计)
│   └── language_manager.py (多语言)
├── 控制层
│   ├── Light_Control.py (光源控制)
│   ├── Spectrometer_Control*.py (光谱仪)
│   └── light_api.py (光源API)
├── 数据处理层
│   ├── colorTrans.py (颜色转换)
│   ├── match_old.py (光谱匹配)
│   ├── linear.py (数学计算)
│   └── write_data.py (数据存储)
└── 配置层
    ├── setting.json (主配置)
    └── language_config.json (语言配置)
```

## 开发工作流

### 1. 代码开发

#### 创建新功能模块
```python
# 示例: 创建新的设备控制模块
# new_device_control.py

class NewDeviceController:
    def __init__(self, config):
        self.config = config
        self.connected = False
    
    def connect(self):
        """连接设备"""
        try:
            # 设备连接逻辑
            self.connected = True
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def measure(self):
        """执行测量"""
        if not self.connected:
            raise Exception("设备未连接")
        # 测量逻辑
        return measurement_data
```

#### 集成到主程序
```python
# 在main.py中集成新模块
from new_device_control import NewDeviceController

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.new_device = NewDeviceController(self.config)
        self.setup_ui()
        self.connect_signals()
```

### 2. 界面开发

#### 使用Qt Designer
1. 打开 `ui/mainwindow.ui`
2. 添加新的控件
3. 设置控件属性和布局
4. 保存UI文件

#### 在代码中使用UI
```python
from PyQt5 import uic

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        uic.loadUi('ui/mainwindow.ui', self)
        self.setup_connections()
    
    def setup_connections(self):
        # 连接信号槽
        self.pushButton.clicked.connect(self.on_button_clicked)
```

### 3. 配置管理

#### 添加新配置项
```json
// setting.json
{
  "channels_num": 31,
  "new_feature_enabled": true,
  "new_device_config": {
    "port": "COM10",
    "baudrate": 115200
  }
}
```

#### 在代码中使用配置
```python
import json

def load_config():
    with open('setting.json', 'r') as f:
        return json.load(f)

config = load_config()
if config.get('new_feature_enabled', False):
    # 启用新功能
    pass
```

## 测试开发

### 单元测试
```python
# test_color_trans.py
import unittest
from colorTrans import wavelength_to_rgb

class TestColorTrans(unittest.TestCase):
    def test_wavelength_to_rgb(self):
        # 测试绿光波长
        rgb = wavelength_to_rgb(550)
        self.assertAlmostEqual(rgb[1], 1.0, places=2)  # 绿色分量应该最大
    
    def test_invalid_wavelength(self):
        # 测试无效波长
        rgb = wavelength_to_rgb(300)  # 超出范围
        self.assertEqual(rgb, (0, 0, 0))

if __name__ == '__main__':
    unittest.main()
```

### 集成测试
```python
# test_integration.py
import unittest
from Light_Control import LightController
from Spectrometer_Control import SpectrometerController

class TestIntegration(unittest.TestCase):
    def setUp(self):
        self.light = LightController("COM9")
        self.spectrometer = SpectrometerController()
    
    def test_measurement_workflow(self):
        # 测试完整的测量流程
        self.assertTrue(self.light.connect())
        self.light.set_channel_intensity(0, 255)
        spectrum = self.spectrometer.measure_spectrum()
        self.assertIsNotNone(spectrum)
```

## 调试技巧

### 1. 日志记录
```python
import logging

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def some_function():
    logger.debug("开始执行函数")
    try:
        # 业务逻辑
        result = complex_calculation()
        logger.info(f"计算结果: {result}")
        return result
    except Exception as e:
        logger.error(f"函数执行失败: {e}")
        raise
```

### 2. 设备模拟
```python
# mock_device.py - 用于测试的模拟设备
class MockSpectrometer:
    def __init__(self):
        self.connected = False
    
    def connect(self):
        self.connected = True
        return True
    
    def measure_spectrum(self):
        # 返回模拟的光谱数据
        import numpy as np
        wavelengths = np.arange(380, 781, 1)
        intensities = np.random.random(len(wavelengths))
        return {'wavelengths': wavelengths, 'intensities': intensities}
```

## 性能优化

### 1. 数据处理优化
```python
import numpy as np
from numba import jit

@jit(nopython=True)
def fast_spectrum_calculation(data):
    """使用Numba加速的光谱计算"""
    result = np.zeros_like(data)
    for i in range(len(data)):
        result[i] = complex_math_operation(data[i])
    return result
```

### 2. 内存管理
```python
import gc

def process_large_dataset(data):
    """处理大数据集时的内存管理"""
    try:
        # 分批处理数据
        batch_size = 1000
        results = []
        
        for i in range(0, len(data), batch_size):
            batch = data[i:i+batch_size]
            batch_result = process_batch(batch)
            results.append(batch_result)
            
            # 强制垃圾回收
            if i % (batch_size * 10) == 0:
                gc.collect()
        
        return np.concatenate(results)
    finally:
        gc.collect()
```

## 打包部署

### 使用提供的构建脚本
```bash
python build_exe.py
```

### 手动打包
```bash
pyinstaller --onefile --windowed --name ColorSpace_Multispectral main.py
```

### 自定义打包配置
```python
# custom_build.py
import PyInstaller.__main__

PyInstaller.__main__.run([
    '--onefile',
    '--windowed',
    '--add-data', 'ui;ui',
    '--add-data', 'translations;translations',
    '--hidden-import', 'scipy.interpolate',
    '--icon', 'icon.ico',
    'main.py'
])
```

## 代码规范

### 1. 命名规范
- 类名: PascalCase (如 `LightController`)
- 函数名: snake_case (如 `measure_spectrum`)
- 常量: UPPER_CASE (如 `MAX_CHANNELS`)
- 私有成员: 前缀下划线 (如 `_internal_method`)

### 2. 文档字符串
```python
def calculate_color_temperature(spectrum):
    """
    计算光谱的相关色温
    
    Args:
        spectrum (numpy.array): 光谱数据数组
        
    Returns:
        float: 相关色温值，单位K
        
    Raises:
        ValueError: 当光谱数据无效时
        
    Example:
        >>> spectrum = np.array([0.1, 0.2, 0.3])
        >>> cct = calculate_color_temperature(spectrum)
        >>> print(f"色温: {cct}K")
    """
    pass
```

### 3. 错误处理
```python
def safe_device_operation():
    """安全的设备操作示例"""
    try:
        device.connect()
        result = device.measure()
        return result
    except DeviceConnectionError:
        logger.error("设备连接失败")
        return None
    except Exception as e:
        logger.error(f"未知错误: {e}")
        raise
    finally:
        device.disconnect()
```

## 扩展开发

### 添加新设备支持
1. 创建设备控制类
2. 实现标准接口
3. 添加配置项
4. 集成到主程序
5. 编写测试用例

### 添加新算法
1. 在相应模块中实现算法
2. 添加算法选择配置
3. 更新UI界面
4. 编写文档和测试

这个开发指南提供了完整的开发流程和最佳实践，帮助开发者快速上手和扩展项目功能。
