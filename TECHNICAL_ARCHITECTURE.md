# ColorSpace Multispectral XM - 技术架构文档

## 系统架构概览

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   主界面 (Qt)    │  │   图表显示      │  │   配置界面      │ │
│  │   mainwindow.ui │  │   PyQtGraph    │  │   Settings     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   颜色转换      │  │   光谱匹配      │  │   数据处理      │ │
│  │   colorTrans   │  │   match_old    │  │   linear       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   语言管理      │  │   许可证验证    │  │   数据写入      │ │
│  │language_manager│  │   license      │  │   write_data   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   设备控制层 (Device Layer)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   光源控制      │  │   光谱仪控制    │  │   串口通信      │ │
│  │ Light_Control  │  │Spectrometer_*  │  │   Serial       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   DMX协议       │  │   光源API       │  │   HID设备       │ │
│  │    pydmx       │  │   light_api    │  │   libclhid     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   硬件抽象层 (Hardware Layer)                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   颜色处理库    │  │   校准库        │  │   3D处理库      │ │
│  │  libclcolor    │  │  libclcalib    │  │  Syunew3D      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. 主程序模块 (main.py)
- **功能**: 应用程序入口点，GUI初始化和事件处理
- **依赖**: PyQt5, 所有业务模块
- **关键类**: MainWindow
- **线程管理**: 使用多线程处理设备通信和数据处理

### 2. 设备控制模块

#### 光源控制 (Light_Control.py)
- **功能**: 控制各种光源设备
- **通信方式**: 串口通信
- **支持设备**: 多通道LED光源

#### 光谱仪控制
- **Spectrometer_Control.py**: 标准光谱仪
- **Spectrometer_Control_IR.py**: 红外光谱仪
- **Spectrometer_Control_IRL.py**: 红外长波光谱仪
- **通信协议**: USB/串口
- **数据格式**: 光谱数据数组

### 3. 数据处理模块

#### 颜色转换 (colorTrans.py)
```python
# 主要功能
- wavelength_to_rgb(): 波长到RGB转换
- RGBToHTMLColor(): RGB到HTML颜色转换
- 支持多种颜色空间转换
```

#### 光谱匹配 (match_old.py)
```python
# 核心算法
- m_matchSpd(): 光谱匹配主函数
- 支持多种匹配算法
- 优化算法性能
```

#### 线性处理 (linear.py)
```python
# 数学计算
- getFunc(): 获取插值函数
- 线性插值和拟合
- 数据平滑处理
```

### 4. 配置管理

#### 主配置 (setting.json)
```json
{
  "channels_num": 31,
  "light_com1": "COM9",
  "protocol": {
    "0": "01 00 00",
    "1": "01 00 01"
  },
  "sp0": "636",
  "sp1": "447"
}
```

#### 语言配置 (language_config.json)
```json
{
  "language": "en"
}
```

## 数据流架构

### 测量数据流
```
硬件设备 → 设备控制层 → 数据处理 → 界面显示
    ↓
  数据存储 ← 文件系统 ← 数据写入模块
```

### 配置数据流
```
配置文件 → 配置管理 → 业务逻辑 → 设备控制
```

## 通信协议

### 串口通信
- **波特率**: 可配置
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

### 设备协议
- **光源控制**: 自定义协议
- **光谱仪**: 标准SCPI协议
- **DMX**: DMX512协议

## 文件系统组织

### 数据文件
- **CSV格式**: 光谱数据存储
- **JSON格式**: 配置和设置
- **Excel格式**: 复杂数据表格

### 临时文件
- **spdTemp/**: 光谱临时数据
- **tempData/**: 其他临时数据
- **缓存机制**: 提高数据访问速度

## 多线程架构

### 主线程
- GUI事件处理
- 用户交互响应

### 工作线程
- 设备通信线程
- 数据处理线程
- 文件I/O线程

### 线程同步
- QMutex: 互斥锁
- QSignal/QSlot: 线程间通信

## 错误处理机制

### 异常处理
- 设备连接异常
- 数据格式异常
- 文件访问异常

### 日志系统
- 操作日志记录
- 错误信息追踪
- 调试信息输出

## 性能优化

### 数据处理优化
- NumPy数组操作
- SciPy科学计算
- 内存管理优化

### 界面响应优化
- 异步数据加载
- 图表实时更新
- 界面缓存机制

## 扩展性设计

### 插件架构
- 模块化设计
- 动态加载机制
- 接口标准化

### 设备扩展
- 统一设备接口
- 驱动程序抽象
- 配置文件扩展

这个技术架构支持多光谱设备的复杂控制需求，具有良好的可维护性和扩展性。
