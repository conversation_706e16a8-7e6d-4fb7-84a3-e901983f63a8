#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口设置持久化测试脚本
用于验证串口下拉框的设置保存和加载功能
"""

import json
import os
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QComboBox
from PyQt5.QtCore import Qt, QTimer

class SerialSettingsTest(QMainWindow):
    """串口设置测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.settings_file = 'test_serial_ui_settings.json'
        self.init_ui()
        
        # 延迟加载设置，模拟主程序的行为
        QTimer.singleShot(100, self.load_settings)
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle('串口设置持久化测试')
        self.setGeometry(100, 100, 500, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加标题
        layout.addWidget(QLabel('串口设置持久化测试'))
        
        # 创建串口下拉框
        self.comboboxes = {}
        combobox_names = [
            'comboBox_serials', 'comboBox_serials_2', 'comboBox_serials_3',
            'comboBox_serials_4', 'comboBox_serials_5', 'comboBox_serials_6',
            'spec_comboBox_serials'
        ]
        
        for name in combobox_names:
            label = QLabel(f'{name}:')
            layout.addWidget(label)
            
            combobox = QComboBox()
            # 添加一些测试串口
            test_ports = ['COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM9', 'COM19', 'COM20']
            for port in test_ports:
                combobox.addItem(port)
            
            # 设置默认值（模拟从setting.json读取的值）
            if name == 'comboBox_serials':
                combobox.setCurrentText('COM9')
            elif name == 'comboBox_serials_2':
                combobox.setCurrentText('COM19')
            elif name == 'comboBox_serials_3':
                combobox.setCurrentText('COM20')
            else:
                combobox.setCurrentText('COM1')
            
            layout.addWidget(combobox)
            self.comboboxes[name] = combobox
        
        # 添加按钮
        save_btn = QPushButton('保存设置')
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        load_btn = QPushButton('加载设置')
        load_btn.clicked.connect(self.load_settings)
        layout.addWidget(load_btn)
        
        clear_btn = QPushButton('清除设置文件')
        clear_btn.clicked.connect(self.clear_settings)
        layout.addWidget(clear_btn)
        
        # 添加状态显示
        self.status_label = QLabel('状态: 就绪')
        layout.addWidget(self.status_label)
    
    def save_settings(self):
        """保存设置"""
        try:
            ui_settings = {
                'comboboxes': {}
            }
            
            print("正在保存串口设置...")
            for name, combobox in self.comboboxes.items():
                current_text = combobox.currentText()
                current_index = combobox.currentIndex()
                ui_settings['comboboxes'][name] = {
                    'current_text': current_text,
                    'current_index': current_index
                }
                print(f"保存 {name}: {current_text} (索引: {current_index})")
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(ui_settings, f, indent=2, ensure_ascii=False)
            
            self.status_label.setText('状态: 设置已保存')
            print("设置保存成功")
            
        except Exception as e:
            self.status_label.setText(f'状态: 保存失败 - {e}')
            print(f"保存设置失败: {e}")
    
    def load_settings(self):
        """加载设置"""
        try:
            if not os.path.exists(self.settings_file):
                self.status_label.setText('状态: 设置文件不存在')
                print("设置文件不存在")
                return
            
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                ui_settings = json.load(f)
            
            if 'comboboxes' in ui_settings:
                print("正在加载串口设置...")
                for name, settings in ui_settings['comboboxes'].items():
                    if name in self.comboboxes:
                        combobox = self.comboboxes[name]
                        text = settings.get('current_text', '')
                        print(f"加载 {name}: {text}")
                        
                        index = combobox.findText(text)
                        if index >= 0:
                            combobox.setCurrentIndex(index)
                            print(f"  成功设置为: {text} (索引: {index})")
                        else:
                            saved_index = settings.get('current_index', 0)
                            if 0 <= saved_index < combobox.count():
                                combobox.setCurrentIndex(saved_index)
                                print(f"  使用索引设置: {saved_index}")
                            else:
                                print(f"  无法恢复设置: {text}")
                    else:
                        print(f"控件不存在: {name}")
            
            self.status_label.setText('状态: 设置已加载')
            print("设置加载成功")
            
        except Exception as e:
            self.status_label.setText(f'状态: 加载失败 - {e}')
            print(f"加载设置失败: {e}")
    
    def clear_settings(self):
        """清除设置文件"""
        try:
            if os.path.exists(self.settings_file):
                os.remove(self.settings_file)
                self.status_label.setText('状态: 设置文件已删除')
                print("设置文件已删除")
            else:
                self.status_label.setText('状态: 设置文件不存在')
                print("设置文件不存在")
        except Exception as e:
            self.status_label.setText(f'状态: 删除失败 - {e}')
            print(f"删除设置文件失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.save_settings()
        event.accept()


def test_serial_settings_logic():
    """测试串口设置逻辑"""
    print("=" * 50)
    print("串口设置持久化逻辑测试")
    print("=" * 50)
    
    # 测试数据
    test_settings = {
        'comboboxes': {
            'comboBox_serials': {
                'current_text': 'COM9',
                'current_index': 5
            },
            'comboBox_serials_2': {
                'current_text': 'COM19',
                'current_index': 6
            },
            'spec_comboBox_serials': {
                'current_text': 'COM1',
                'current_index': 0
            }
        }
    }
    
    # 测试保存
    try:
        with open('test_logic_settings.json', 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2, ensure_ascii=False)
        print("✓ 设置保存测试通过")
    except Exception as e:
        print(f"✗ 设置保存测试失败: {e}")
        return False
    
    # 测试加载
    try:
        with open('test_logic_settings.json', 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        # 验证数据
        if loaded_settings == test_settings:
            print("✓ 设置加载测试通过")
        else:
            print("✗ 设置加载测试失败: 数据不匹配")
            return False
            
    except Exception as e:
        print(f"✗ 设置加载测试失败: {e}")
        return False
    
    # 清理测试文件
    try:
        os.remove('test_logic_settings.json')
        print("✓ 测试文件清理完成")
    except:
        pass
    
    print("=" * 50)
    print("所有逻辑测试通过！")
    print("=" * 50)
    return True


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--logic':
        # 逻辑测试模式
        success = test_serial_settings_logic()
        sys.exit(0 if success else 1)
    else:
        # GUI测试模式
        app = QApplication(sys.argv)
        window = SerialSettingsTest()
        window.show()
        
        print("=" * 50)
        print("串口设置持久化GUI测试")
        print("=" * 50)
        print("使用说明:")
        print("1. 调整各个串口下拉框的选择")
        print("2. 点击'保存设置'按钮")
        print("3. 点击'加载设置'按钮验证")
        print("4. 关闭窗口会自动保存设置")
        print("5. 重新打开程序验证设置是否保持")
        print("=" * 50)
        
        sys.exit(app.exec_())


if __name__ == '__main__':
    main()
