# ColorSpace Multispectral XM Project Index

## 项目概述
这是一个多光谱颜色空间控制软件项目，基于PyQt5开发，用于控制多光谱设备和进行颜色测量分析。

## 核心文件结构

### 主程序文件
- **main.py** - 主程序入口，包含GUI界面和核心逻辑
- **main2.py** - 备用主程序文件

### 控制模块
- **Light_Control.py** - 光源控制模块
- **Spectrometer_Control.py** - 光谱仪控制模块
- **Spectrometer_Control_IR.py** - 红外光谱仪控制
- **Spectrometer_Control_IRL.py** - 红外长波光谱仪控制
- **light_api.py** - 光源API接口
- **pydmx.py** - DMX协议控制

### 数据处理模块
- **colorTrans.py** - 颜色转换功能（波长到RGB转换等）
- **linear.py** - 线性插值和数学计算
- **match_old.py** - 光谱匹配算法（旧版本）
- **match_new.py** - 光谱匹配算法（新版本）
- **measure_math_control.py** - 测量数学控制
- **write_data.py** - 数据写入功能

### 配置和设置
- **setting.json** - 主配置文件（包含通道数、串口配置、光谱参数等）
- **setting_*.json** - 各种配置文件的备份版本
- **language_config.json** - 语言配置
- **language_manager.py** - 语言管理器

### 用户界面
- **ui/mainwindow.ui** - Qt Designer设计的主界面文件
- **ui/CMS.qrc** - Qt资源文件
- **ui/Source/** - UI相关的资源文件

### 翻译和国际化
- **translations/zh.json** - 中文翻译
- **translations/en.json** - 英文翻译

### 数据目录
- **data/** - 数据文件目录
  - **cct_data.xlsx** - 色温数据
  - **illum_data.xlsx** - 光源数据
  - **light*.json** - 各种光源配置文件

- **CIE_illminants/** - CIE标准光源数据
  - 包含各种标准光源的光谱数据文件

- **ledData/** - LED数据目录
- **tempData/** - 临时数据目录
- **spdTemp/** - 光谱临时数据（大量CSV文件）

### 测试和校准数据
- **28号28通道/** - 28通道测试数据
- **ok_spec/** - 校准光谱数据
- **间隔时间长26/** - 长间隔时间测试数据

### 构建和部署
- **build_exe.py** - PyInstaller构建脚本
- **build_spec.py** - 构建规格文件
- **build.bat** - 构建批处理文件
- **install.txt** - 安装说明
- **ColorSpace_Multispectral.spec** - PyInstaller规格文件

### 库文件
- **libclapi.dll** - 颜色API动态库
- **libclapi.lib** - 颜色API静态库
- **libclcalib.dll** - 颜色校准库
- **libclcolor.dll** - 颜色处理库
- **libclhid.dll** - HID设备库
- **Syunew3D_x64.dll** - 3D处理库

### 工具和实用程序
- **order.py** - 指令处理
- **license.py** - 许可证验证
- **Psyunew3.py** - 心理物理学相关功能
- **DATA.py** - 数据处理
- **Test_Measure.py** - 测量测试

### 测试文件
- **test*.py** - 各种测试脚本
- **test*.txt** - 测试数据文件
- **test.ipynb** - Jupyter notebook测试文件

### 文档
- **打包说明.md** - 打包说明文档
- **构建成功说明.md** - 构建成功说明
- **标签翻译更新说明.md** - 标签翻译更新说明
- **消息翻译修复说明.md** - 消息翻译修复说明
- **语言切换功能说明.md** - 语言切换功能说明
- **多光谱软件处理问题.txt** - 问题处理说明
- **临时说明.txt** - 临时说明文档
- **UI_SETTINGS_GUIDE.md** - UI设置持久化功能说明

### UI设置持久化
- **ui_settings.json** - UI设置配置文件（自动生成）
- **ui_settings_example.json** - UI设置示例文件
- **test_ui_settings.py** - UI设置功能测试脚本

## 技术栈
- **GUI框架**: PyQt5
- **数据处理**: NumPy, SciPy, Matplotlib
- **图表显示**: PyQtGraph
- **串口通信**: PySerial
- **数据格式**: JSON, CSV, Excel
- **构建工具**: PyInstaller

## 主要功能模块
1. **多光谱设备控制** - 控制各种光谱仪和光源
2. **颜色空间转换** - 支持多种颜色空间转换
3. **光谱数据分析** - 光谱匹配和分析算法
4. **数据可视化** - 实时图表显示
5. **多语言支持** - 中英文界面切换
6. **数据导入导出** - 支持多种数据格式
7. **UI设置持久化** - 自动保存和恢复界面设置

## 配置说明
- 主配置文件为 `setting.json`，包含31个通道的配置
- 支持多个串口设备连接
- 可配置的光谱参数和协议设置
- 支持不同的光源和测量模式

这个项目是一个完整的多光谱颜色测量和控制系统，具有专业的光学测量功能和用户友好的界面。
