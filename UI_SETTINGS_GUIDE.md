# UI设置持久化功能说明

## 功能概述

本功能为ColorSpace Multispectral XM软件添加了界面设置的持久化能力，能够在程序关闭时自动保存界面状态，并在下次启动时恢复这些设置。

## 主要特性

### 1. 自动保存的设置项

#### 窗口设置
- **窗口大小和位置**: 保存窗口的x、y坐标以及宽度、高度
- **最大化状态**: 记录窗口是否处于最大化状态
- **窗口状态**: 保存完整的窗口状态信息

#### 控件状态
- **复选框状态**: 保存所有复选框的选中/未选中状态
  - 通道复选框 (checkBox_ch1 到 checkBox_ch6)
  - 发送模式单选按钮 (radioButton_send_true/false)
  
- **下拉框选择**: 保存下拉框的当前选择项
  - 串口选择框 (comboBox_serials 系列)
  - 光谱仪串口选择 (spec_comboBox_serials)
  - LED数据选择 (comboBox_ledData)

- **标签页选择**: 保存各个标签页容器的当前选中页面
  - 主标签页 (tabWidget 系列)

- **滑块值**: 保存所有通道滑块的当前值
  - 通道滑块 (verticalSlider_1 到 verticalSlider_31)
  - 总调控滑块 (verticalSlider_32)

- **输入框内容**: 保存输入框中的文本内容
  - 通道输入框 (lineEdit_1 到 lineEdit_32)
  - 通道配置 (lineEdit_channels)
  - 节点配置 (lineEdit_nodes)

- **分割器位置**: 保存界面分割器的位置设置

### 2. 配置文件

#### 文件位置
- **主配置文件**: `ui_settings.json` (自动生成)
- **示例文件**: `ui_settings_example.json` (参考模板)

#### 文件结构
```json
{
  "window": {
    "geometry": { "x": 100, "y": 100, "width": 1686, "height": 1076 },
    "maximized": true,
    "window_state": 2
  },
  "checkboxes": {
    "checkBox_ch1": true,
    "radioButton_send_true": true
  },
  "comboboxes": {
    "comboBox_serials": {
      "current_text": "COM9",
      "current_index": 0
    }
  },
  "tabwidgets": {
    "tabWidget": 0
  },
  "sliders": {
    "verticalSlider_1": 0
  },
  "lineedits": {
    "lineEdit_1": "0.0"
  },
  "splitters": {
    "splitter": [400, 800]
  }
}
```

## 使用方法

### 1. 自动使用
- **保存**: 程序关闭时自动保存当前界面设置
- **加载**: 程序启动时自动加载保存的设置
- **无需手动操作**: 完全透明的后台运行

### 2. 手动管理

#### 删除设置文件
如果需要重置为默认设置，可以删除 `ui_settings.json` 文件：
```bash
# Windows
del ui_settings.json

# Linux/Mac
rm ui_settings.json
```

#### 备份设置
```bash
# 备份当前设置
copy ui_settings.json ui_settings_backup.json

# 恢复设置
copy ui_settings_backup.json ui_settings.json
```

## 错误处理

### 1. 配置文件不存在
- **行为**: 程序正常启动，使用默认设置
- **日志**: 输出 "UI设置文件不存在，使用默认设置"

### 2. 配置文件损坏
- **行为**: 程序正常启动，使用默认设置
- **日志**: 输出错误信息和堆栈跟踪
- **建议**: 删除损坏的配置文件，让程序重新生成

### 3. 保存失败
- **行为**: 程序正常关闭，但设置不会保存
- **日志**: 输出错误信息
- **原因**: 可能是磁盘空间不足或权限问题

## 技术实现

### 1. 核心方法

#### save_ui_settings()
```python
def save_ui_settings(self):
    """保存界面设置到配置文件"""
    # 收集所有界面控件的状态
    # 序列化为JSON格式
    # 写入ui_settings.json文件
```

#### load_ui_settings()
```python
def load_ui_settings(self):
    """从配置文件加载界面设置"""
    # 读取ui_settings.json文件
    # 解析JSON数据
    # 应用到相应的界面控件
```

#### closeEvent()
```python
def closeEvent(self, event):
    """窗口关闭事件，保存UI设置"""
    # 在窗口关闭前保存设置
    # 确保即使保存失败也能正常关闭
```

### 2. 调用时机

#### 程序启动时
```python
def __init__(self):
    # ... 其他初始化代码 ...
    self.load_ui_settings()  # 加载UI设置
```

#### 程序关闭时
```python
# 在主程序中设置closeEvent处理器
stats.ui.closeEvent = closeEvent_wrapper
```

## 扩展和定制

### 1. 添加新的控件支持

如果需要保存新的控件状态，可以在 `save_ui_settings()` 和 `load_ui_settings()` 方法中添加相应代码：

```python
# 在save_ui_settings()中添加
ui_settings['new_controls'] = {}
if hasattr(self.ui, 'new_control_name'):
    control = getattr(self.ui, 'new_control_name')
    ui_settings['new_controls']['new_control_name'] = control.value()

# 在load_ui_settings()中添加
if 'new_controls' in ui_settings:
    for control_name, value in ui_settings['new_controls'].items():
        if hasattr(self.ui, control_name):
            control = getattr(self.ui, control_name)
            control.setValue(value)
```

### 2. 自定义配置文件位置

可以修改配置文件的保存位置：

```python
# 修改文件路径
config_file = os.path.join(os.path.expanduser('~'), '.colorspace', 'ui_settings.json')
```

### 3. 添加版本控制

可以在配置文件中添加版本信息，以便处理不同版本间的兼容性：

```python
ui_settings['version'] = '1.0'
ui_settings['app_version'] = '3.7'
```

## 注意事项

1. **权限问题**: 确保程序有权限在当前目录创建和修改文件
2. **磁盘空间**: 配置文件通常很小，但仍需确保有足够的磁盘空间
3. **编码问题**: 配置文件使用UTF-8编码，支持中文等特殊字符
4. **备份重要设置**: 对于重要的界面配置，建议定期备份
5. **版本兼容性**: 不同版本的软件可能有不同的界面控件，加载时会自动跳过不存在的控件

## 故障排除

### 问题1: 设置没有保存
**可能原因**: 
- 程序异常退出
- 磁盘空间不足
- 权限不足

**解决方案**:
- 检查程序日志
- 确保有足够的磁盘空间
- 以管理员权限运行程序

### 问题2: 设置加载失败
**可能原因**:
- 配置文件损坏
- JSON格式错误
- 文件编码问题

**解决方案**:
- 删除ui_settings.json文件
- 检查文件内容是否为有效JSON
- 使用示例文件作为模板

### 问题3: 界面显示异常
**可能原因**:
- 保存的窗口位置超出屏幕范围
- 控件状态不兼容

**解决方案**:
- 删除配置文件重置设置
- 手动编辑配置文件修正数值
- 检查屏幕分辨率变化

这个UI设置持久化功能大大提升了用户体验，让用户的个性化设置能够在程序重启后得到保持。
