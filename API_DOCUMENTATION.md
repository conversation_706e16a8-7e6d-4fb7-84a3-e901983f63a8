# ColorSpace Multispectral XM - API文档

## 核心API接口

### 1. 颜色转换模块 (colorTrans.py)

#### wavelength_to_rgb(wavelength, gamma=0.5)
将波长转换为RGB颜色值

**参数:**
- `wavelength` (float): 波长值，单位纳米 (380-750nm)
- `gamma` (float): 伽马校正值，默认0.5

**返回值:**
- `tuple`: (R, G, B) 颜色值，范围0-1

**示例:**
```python
from colorTrans import wavelength_to_rgb
rgb = wavelength_to_rgb(550)  # 绿光
print(rgb)  # (0.0, 1.0, 0.0)
```

#### RGBToHTMLColor(rgb_tuple)
将RGB元组转换为HTML颜色代码

**参数:**
- `rgb_tuple` (tuple): (R, G, B) 颜色值

**返回值:**
- `str`: HTML颜色代码，如 "#FF0000"

### 2. 光源控制模块 (Light_Control.py)

#### LightController类

##### __init__(com_port, baudrate=9600)
初始化光源控制器

**参数:**
- `com_port` (str): 串口号，如 "COM9"
- `baudrate` (int): 波特率，默认9600

##### connect()
连接光源设备

**返回值:**
- `bool`: 连接成功返回True，失败返回False

##### set_channel_intensity(channel, intensity)
设置指定通道的光强

**参数:**
- `channel` (int): 通道号 (0-30)
- `intensity` (int): 光强值 (0-255)

**返回值:**
- `bool`: 设置成功返回True

##### get_channel_status(channel)
获取通道状态

**参数:**
- `channel` (int): 通道号

**返回值:**
- `dict`: 包含通道状态信息

### 3. 光谱仪控制模块 (Spectrometer_Control.py)

#### SpectrometerController类

##### __init__(device_id=None)
初始化光谱仪控制器

**参数:**
- `device_id` (str): 设备ID，可选

##### measure_spectrum()
测量光谱数据

**返回值:**
- `numpy.array`: 光谱数据数组

##### set_integration_time(time_ms)
设置积分时间

**参数:**
- `time_ms` (int): 积分时间，单位毫秒

##### calibrate()
执行光谱仪校准

**返回值:**
- `bool`: 校准成功返回True

### 4. 光谱匹配模块 (match_old.py)

#### m_matchSpd(target_spectrum, reference_spectra, method='least_squares')
光谱匹配算法

**参数:**
- `target_spectrum` (array): 目标光谱
- `reference_spectra` (array): 参考光谱集合
- `method` (str): 匹配方法，默认'least_squares'

**返回值:**
- `dict`: 匹配结果，包含匹配系数和误差

**示例:**
```python
from match_old import m_matchSpd
result = m_matchSpd(target, references)
print(f"匹配误差: {result['error']}")
```

### 5. 线性处理模块 (linear.py)

#### getFunc(x_data, y_data, kind='linear')
获取插值函数

**参数:**
- `x_data` (array): X轴数据
- `y_data` (array): Y轴数据
- `kind` (str): 插值类型，默认'linear'

**返回值:**
- `function`: 插值函数对象

### 6. 语言管理模块 (language_manager.py)

#### LanguageManager类

##### __init__(config_file='language_config.json')
初始化语言管理器

##### get_text(key, default=None)
获取翻译文本

**参数:**
- `key` (str): 文本键值
- `default` (str): 默认文本

**返回值:**
- `str`: 翻译后的文本

##### set_language(language_code)
设置当前语言

**参数:**
- `language_code` (str): 语言代码，如 'en', 'zh'

### 7. 数据写入模块 (write_data.py)

#### save_spectrum_data(data, filename, format='csv')
保存光谱数据

**参数:**
- `data` (array): 光谱数据
- `filename` (str): 文件名
- `format` (str): 文件格式，默认'csv'

#### load_spectrum_data(filename)
加载光谱数据

**参数:**
- `filename` (str): 文件名

**返回值:**
- `array`: 光谱数据数组

### 8. 测量数学控制模块 (measure_math_control.py)

#### MeasurementController类

##### calculate_color_parameters(spectrum)
计算颜色参数

**参数:**
- `spectrum` (array): 光谱数据

**返回值:**
- `dict`: 颜色参数字典，包含XYZ, Lab, RGB等

##### calculate_cct(spectrum)
计算相关色温

**参数:**
- `spectrum` (array): 光谱数据

**返回值:**
- `float`: 相关色温值(K)

## 配置API

### 配置文件结构 (setting.json)

```json
{
  "channels_num": 31,
  "light_com1": "COM9",
  "light_com2": "COM19",
  "protocol": {
    "0": "01 00 00",
    "1": "01 00 01"
  },
  "sp0": "636",
  "sp1": "447"
}
```

### 配置访问方法

#### load_config(config_file='setting.json')
加载配置文件

#### save_config(config_dict, config_file='setting.json')
保存配置文件

#### get_config_value(key, default=None)
获取配置值

## 事件系统

### PyQt5信号槽机制

#### 自定义信号
```python
from PyQt5.QtCore import pyqtSignal

class CustomSignals(QObject):
    spectrum_measured = pyqtSignal(object)  # 光谱测量完成
    device_connected = pyqtSignal(str)      # 设备连接
    error_occurred = pyqtSignal(str)        # 错误发生
```

#### 信号连接示例
```python
signals = CustomSignals()
signals.spectrum_measured.connect(self.on_spectrum_measured)
signals.device_connected.connect(self.on_device_connected)
```

## 错误处理

### 异常类型

#### DeviceConnectionError
设备连接异常

#### SpectrumMeasurementError
光谱测量异常

#### ConfigurationError
配置文件异常

### 错误处理示例
```python
try:
    controller = LightController("COM9")
    controller.connect()
except DeviceConnectionError as e:
    print(f"设备连接失败: {e}")
```

## 数据格式

### 光谱数据格式
```python
{
    "wavelengths": [380, 381, 382, ...],  # 波长数组
    "intensities": [0.1, 0.2, 0.3, ...], # 强度数组
    "timestamp": "2025-08-01T10:30:00",   # 时间戳
    "metadata": {                         # 元数据
        "integration_time": 100,
        "device_id": "SPEC001"
    }
}
```

### 颜色参数格式
```python
{
    "XYZ": [0.3127, 0.3290, 0.3583],
    "Lab": [53.23, 80.11, 67.22],
    "RGB": [255, 0, 128],
    "CCT": 6500,
    "CRI": 85.2
}
```

这些API接口提供了完整的多光谱设备控制和数据处理功能，支持灵活的扩展和定制。
