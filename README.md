# ColorSpace Multispectral XM

一个专业的多光谱颜色空间控制软件，基于PyQt5开发，用于控制多光谱设备和进行精确的颜色测量分析。

## 🌟 主要特性

- **多设备支持**: 支持多种光谱仪和光源设备
- **实时测量**: 实时光谱数据采集和显示
- **颜色空间转换**: 支持多种颜色空间转换 (XYZ, Lab, RGB等)
- **光谱匹配**: 先进的光谱匹配算法
- **多语言界面**: 支持中英文界面切换
- **数据可视化**: 实时图表显示和数据分析
- **配置灵活**: 支持31通道配置和多种设备协议

## 📋 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8+
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 5GB可用空间

## 🚀 快速开始

### 安装依赖
```bash
pip install PyQt5 numpy scipy matplotlib pyqtgraph pyserial openpyxl
```

### 运行程序
```bash
python main.py
```

### 构建可执行文件
```bash
python build_exe.py
```

## 📁 项目结构

```
ColorSpace_Multispectral_XM/
├── main.py                    # 主程序入口
├── ui/                        # 用户界面文件
│   └── mainwindow.ui         # Qt Designer界面文件
├── translations/              # 多语言翻译文件
│   ├── zh.json               # 中文翻译
│   └── en.json               # 英文翻译
├── data/                      # 数据文件目录
├── CIE_illminants/           # CIE标准光源数据
├── spdTemp/                  # 光谱临时数据
├── 控制模块/
│   ├── Light_Control.py      # 光源控制
│   ├── Spectrometer_Control*.py # 光谱仪控制
│   └── light_api.py          # 光源API
├── 数据处理模块/
│   ├── colorTrans.py         # 颜色转换
│   ├── match_old.py          # 光谱匹配
│   ├── linear.py             # 数学计算
│   └── write_data.py         # 数据存储
└── 配置文件/
    ├── setting.json          # 主配置文件
    └── language_config.json  # 语言配置
```

## 🔧 核心功能

### 设备控制
- **光源控制**: 支持多通道LED光源控制
- **光谱仪**: 支持标准光谱仪、红外光谱仪
- **串口通信**: 灵活的串口设备通信
- **DMX协议**: 支持DMX512协议设备

### 数据处理
- **光谱分析**: 专业的光谱数据处理算法
- **颜色计算**: 精确的颜色参数计算
- **数据插值**: 高精度数据插值和平滑
- **匹配算法**: 先进的光谱匹配技术

### 用户界面
- **实时显示**: 实时光谱曲线和颜色显示
- **参数设置**: 直观的参数配置界面
- **数据导出**: 支持多种格式数据导出
- **多语言**: 中英文界面无缝切换

## 📖 文档

- [项目索引](PROJECT_INDEX.md) - 完整的项目文件索引
- [技术架构](TECHNICAL_ARCHITECTURE.md) - 系统架构和设计
- [API文档](API_DOCUMENTATION.md) - 详细的API接口说明
- [开发指南](DEVELOPMENT_GUIDE.md) - 开发环境设置和扩展指南

## 🛠️ 配置说明

### 主配置文件 (setting.json)
```json
{
  "channels_num": 31,           // 通道数量
  "light_com1": "COM9",         // 光源串口1
  "light_com2": "COM19",        // 光源串口2
  "protocol": {                 // 通信协议
    "0": "01 00 00",
    "1": "01 00 01"
  },
  "sp0": "636",                 // 光谱参数
  "sp1": "447"
}
```

### 语言配置 (language_config.json)
```json
{
  "language": "en"              // 界面语言: "en" 或 "zh"
}
```

## 🔬 使用示例

### 基本测量流程
```python
from Light_Control import LightController
from Spectrometer_Control import SpectrometerController

# 初始化设备
light = LightController("COM9")
spectrometer = SpectrometerController()

# 连接设备
light.connect()
spectrometer.connect()

# 设置光源
light.set_channel_intensity(0, 255)

# 测量光谱
spectrum = spectrometer.measure_spectrum()

# 处理数据
from colorTrans import wavelength_to_rgb
from match_old import m_matchSpd

# 颜色转换
rgb = wavelength_to_rgb(550)

# 光谱匹配
result = m_matchSpd(spectrum, reference_spectra)
```

## 🧪 测试

运行单元测试:
```bash
python -m pytest tests/
```

运行特定测试:
```bash
python test_color_trans.py
```

## 📦 打包部署

### 自动构建
```bash
python build_exe.py
```

### 手动构建
```bash
pyinstaller --onefile --windowed --icon icon.ico main.py
```

构建完成后，可执行文件位于 `dist/` 目录中。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📝 开发规范

- 遵循PEP 8代码规范
- 添加适当的文档字符串
- 编写单元测试
- 使用有意义的提交信息

## 🐛 问题报告

如果您发现任何问题，请在 [Issues](../../issues) 页面报告。

## 📄 许可证

本项目采用专有许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

## 📞 联系方式

- 项目维护者: [维护者姓名]
- 邮箱: [联系邮箱]
- 技术支持: [支持渠道]

---

**ColorSpace Multispectral XM** - 专业的多光谱颜色测量解决方案

*最后更新: 2025年8月1日*
