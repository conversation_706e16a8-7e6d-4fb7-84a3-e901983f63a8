#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI设置持久化功能测试脚本
用于验证UI设置的保存和加载功能是否正常工作
"""

import json
import os
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QCheckBox, QComboBox, QSlider, QLineEdit
from PyQt5.QtCore import Qt

class TestUISettings:
    """UI设置测试类"""
    
    def __init__(self):
        self.settings_file = 'test_ui_settings.json'
    
    def test_save_settings(self):
        """测试保存设置功能"""
        print("测试保存UI设置...")
        
        # 创建测试设置数据
        test_settings = {
            'window': {
                'geometry': {'x': 100, 'y': 100, 'width': 800, 'height': 600},
                'maximized': False
            },
            'checkboxes': {
                'checkBox_ch1': True,
                'checkBox_ch2': False
            },
            'comboboxes': {
                'comboBox_serials': {
                    'current_text': 'COM9',
                    'current_index': 0
                }
            },
            'sliders': {
                'verticalSlider_1': 500
            },
            'lineedits': {
                'lineEdit_1': '50.0'
            }
        }
        
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(test_settings, f, indent=2, ensure_ascii=False)
            print("✓ 设置保存成功")
            return True
        except Exception as e:
            print(f"✗ 设置保存失败: {e}")
            return False
    
    def test_load_settings(self):
        """测试加载设置功能"""
        print("测试加载UI设置...")
        
        try:
            if not os.path.exists(self.settings_file):
                print("✗ 设置文件不存在")
                return False
            
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            # 验证设置数据
            expected_keys = ['window', 'checkboxes', 'comboboxes', 'sliders', 'lineedits']
            for key in expected_keys:
                if key not in settings:
                    print(f"✗ 缺少设置项: {key}")
                    return False
            
            print("✓ 设置加载成功")
            print(f"  窗口大小: {settings['window']['geometry']['width']}x{settings['window']['geometry']['height']}")
            print(f"  复选框状态: {settings['checkboxes']}")
            print(f"  下拉框选择: {settings['comboboxes']['comboBox_serials']['current_text']}")
            return True
            
        except Exception as e:
            print(f"✗ 设置加载失败: {e}")
            return False
    
    def test_json_format(self):
        """测试JSON格式正确性"""
        print("测试JSON格式...")
        
        try:
            if not os.path.exists(self.settings_file):
                print("✗ 设置文件不存在")
                return False
            
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试解析JSON
            json.loads(content)
            print("✓ JSON格式正确")
            return True
            
        except json.JSONDecodeError as e:
            print(f"✗ JSON格式错误: {e}")
            return False
        except Exception as e:
            print(f"✗ 文件读取错误: {e}")
            return False
    
    def test_file_permissions(self):
        """测试文件权限"""
        print("测试文件权限...")
        
        try:
            # 测试写权限
            test_file = 'test_permission.tmp'
            with open(test_file, 'w') as f:
                f.write('test')
            
            # 测试读权限
            with open(test_file, 'r') as f:
                content = f.read()
            
            # 清理测试文件
            os.remove(test_file)
            
            print("✓ 文件权限正常")
            return True
            
        except Exception as e:
            print(f"✗ 文件权限错误: {e}")
            return False
    
    def cleanup(self):
        """清理测试文件"""
        try:
            if os.path.exists(self.settings_file):
                os.remove(self.settings_file)
            print("✓ 测试文件已清理")
        except Exception as e:
            print(f"✗ 清理测试文件失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 50)
        print("UI设置持久化功能测试")
        print("=" * 50)
        
        tests = [
            self.test_file_permissions,
            self.test_save_settings,
            self.test_load_settings,
            self.test_json_format
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                print()
            except Exception as e:
                print(f"✗ 测试异常: {e}")
                print()
        
        print("=" * 50)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("✓ 所有测试通过！UI设置功能正常")
        else:
            print("✗ 部分测试失败，请检查相关功能")
        
        print("=" * 50)
        
        # 清理测试文件
        self.cleanup()
        
        return passed == total


class SimpleTestWindow(QMainWindow):
    """简单的测试窗口，用于验证UI设置功能"""
    
    def __init__(self):
        super().__init__()
        self.settings_file = 'simple_test_ui_settings.json'
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle('UI设置测试窗口')
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 添加测试控件
        layout.addWidget(QLabel('UI设置持久化测试'))
        
        self.checkbox = QCheckBox('测试复选框')
        layout.addWidget(self.checkbox)
        
        self.combobox = QComboBox()
        self.combobox.addItems(['选项1', '选项2', '选项3'])
        layout.addWidget(self.combobox)
        
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(0, 100)
        layout.addWidget(self.slider)
        
        self.lineedit = QLineEdit('测试文本')
        layout.addWidget(self.lineedit)
        
        save_btn = QPushButton('保存设置')
        save_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_btn)
        
        load_btn = QPushButton('加载设置')
        load_btn.clicked.connect(self.load_settings)
        layout.addWidget(load_btn)
    
    def save_settings(self):
        """保存设置"""
        settings = {
            'window': {
                'geometry': {
                    'x': self.x(),
                    'y': self.y(),
                    'width': self.width(),
                    'height': self.height()
                }
            },
            'checkbox': self.checkbox.isChecked(),
            'combobox': {
                'text': self.combobox.currentText(),
                'index': self.combobox.currentIndex()
            },
            'slider': self.slider.value(),
            'lineedit': self.lineedit.text()
        }
        
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            print("设置已保存")
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def load_settings(self):
        """加载设置"""
        try:
            if not os.path.exists(self.settings_file):
                print("设置文件不存在")
                return
            
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            # 恢复窗口位置和大小
            if 'window' in settings:
                geo = settings['window']['geometry']
                self.setGeometry(geo['x'], geo['y'], geo['width'], geo['height'])
            
            # 恢复控件状态
            if 'checkbox' in settings:
                self.checkbox.setChecked(settings['checkbox'])
            
            if 'combobox' in settings:
                index = settings['combobox']['index']
                if 0 <= index < self.combobox.count():
                    self.combobox.setCurrentIndex(index)
            
            if 'slider' in settings:
                self.slider.setValue(settings['slider'])
            
            if 'lineedit' in settings:
                self.lineedit.setText(settings['lineedit'])
            
            print("设置已加载")
            
        except Exception as e:
            print(f"加载设置失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.save_settings()
        event.accept()


def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--gui':
        # GUI测试模式
        app = QApplication(sys.argv)
        window = SimpleTestWindow()
        window.show()
        sys.exit(app.exec_())
    else:
        # 命令行测试模式
        tester = TestUISettings()
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
