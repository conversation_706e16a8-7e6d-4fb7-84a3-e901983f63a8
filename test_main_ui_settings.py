#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序的UI设置功能
"""

import sys
import os
import json
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QComboBox
from PyQt5.QtCore import Qt, QTimer

def test_ui_settings_file():
    """测试UI设置文件的创建和读取"""
    print("=== 测试UI设置文件功能 ===")
    
    # 创建测试设置
    test_settings = {
        'window': {
            'geometry': {'x': 100, 'y': 100, 'width': 800, 'height': 600},
            'maximized': False
        },
        'comboboxes': {
            'comboBox_serials': {
                'current_text': 'COM9',
                'current_index': 0
            },
            'comboBox_serials_2': {
                'current_text': 'COM19',
                'current_index': 1
            }
        }
    }
    
    # 测试保存
    try:
        with open('ui_settings.json', 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2, ensure_ascii=False)
        print("✓ 测试设置文件创建成功")
    except Exception as e:
        print(f"✗ 测试设置文件创建失败: {e}")
        return False
    
    # 测试读取
    try:
        with open('ui_settings.json', 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        if loaded_settings == test_settings:
            print("✓ 测试设置文件读取成功")
        else:
            print("✗ 测试设置文件读取失败: 数据不匹配")
            return False
    except Exception as e:
        print(f"✗ 测试设置文件读取失败: {e}")
        return False
    
    print("✓ UI设置文件功能正常")
    return True

def check_main_modifications():
    """检查main.py的修改是否正确"""
    print("=== 检查main.py修改 ===")
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ('QTimer导入', 'from PyQt5.QtCore import QObject, pyqtSignal, QTimer'),
            ('延迟加载UI设置', 'QTimer.singleShot(100, self.load_ui_settings)'),
            ('save_ui_settings方法', 'def save_ui_settings(self):'),
            ('load_ui_settings方法', 'def load_ui_settings(self):'),
            ('closeEvent方法', 'def closeEvent(self, event):'),
            ('closeEvent包装器', 'def closeEvent_wrapper(event):'),
        ]
        
        all_passed = True
        for check_name, check_text in checks:
            if check_text in content:
                print(f"✓ {check_name}: 存在")
            else:
                print(f"✗ {check_name}: 缺失")
                all_passed = False
        
        if all_passed:
            print("✓ main.py修改检查通过")
        else:
            print("✗ main.py修改检查失败")
        
        return all_passed
        
    except Exception as e:
        print(f"✗ 检查main.py失败: {e}")
        return False

def simulate_main_behavior():
    """模拟主程序的行为"""
    print("=== 模拟主程序行为 ===")
    
    class MockStats:
        def __init__(self):
            print("MockStats初始化...")
            self.ui = MockUI()
            # 模拟延迟加载
            QTimer.singleShot(100, self.load_ui_settings)
        
        def load_ui_settings(self):
            print("load_ui_settings被调用")
            if os.path.exists('ui_settings.json'):
                try:
                    with open('ui_settings.json', 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                    print(f"加载设置: {settings}")
                    return True
                except Exception as e:
                    print(f"加载设置失败: {e}")
                    return False
            else:
                print("设置文件不存在")
                return False
        
        def save_ui_settings(self):
            print("save_ui_settings被调用")
            settings = {
                'test': 'data',
                'timestamp': time.time()
            }
            try:
                with open('ui_settings.json', 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=2)
                print(f"保存设置: {settings}")
                return True
            except Exception as e:
                print(f"保存设置失败: {e}")
                return False
        
        def closeEvent(self, event):
            print("closeEvent被调用")
            self.save_ui_settings()
            event.accept()
    
    class MockUI:
        def __init__(self):
            self.closed = False
        
        def closeEvent(self, event):
            print("MockUI.closeEvent被调用")
            self.closed = True
        
        def showMaximized(self):
            print("showMaximized被调用")
    
    class MockEvent:
        def __init__(self):
            self.accepted = False
        
        def accept(self):
            self.accepted = True
        
        def isAccepted(self):
            return self.accepted
    
    # 模拟主程序流程
    app = QApplication([])
    stats = MockStats()
    
    # 模拟closeEvent包装器
    original_closeEvent = stats.ui.closeEvent
    def closeEvent_wrapper(event):
        stats.closeEvent(event)
        if event.isAccepted():
            original_closeEvent(event)
    
    stats.ui.closeEvent = closeEvent_wrapper
    stats.ui.showMaximized()
    
    # 等待延迟加载完成
    QTimer.singleShot(200, app.quit)
    app.exec_()
    
    # 模拟关闭事件
    event = MockEvent()
    stats.ui.closeEvent(event)
    
    print("✓ 主程序行为模拟完成")
    return True

def cleanup_test_files():
    """清理测试文件"""
    try:
        if os.path.exists('ui_settings.json'):
            os.remove('ui_settings.json')
        print("✓ 测试文件清理完成")
    except Exception as e:
        print(f"清理测试文件失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("主程序UI设置功能测试")
    print("=" * 50)
    
    tests = [
        test_ui_settings_file,
        check_main_modifications,
        simulate_main_behavior
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！")
        print("建议:")
        print("1. 运行主程序测试UI设置功能")
        print("2. 调整界面设置后关闭程序")
        print("3. 重新打开程序验证设置是否保持")
    else:
        print("✗ 部分测试失败")
        print("请检查main.py的修改是否正确")
    
    print("=" * 50)
    
    # 清理测试文件
    cleanup_test_files()
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
